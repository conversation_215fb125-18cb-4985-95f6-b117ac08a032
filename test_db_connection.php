<?php
/**
 * 資料庫連接測試檔案
 * 用於診斷 cPanel 環境下的資料庫連接問題
 */

// 顯示所有錯誤
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>資料庫連接測試</h2>";

// 包含配置檔案
require_once 'php/config.php';

echo "<h3>1. 配置檢查</h3>";
echo "DB_HOST: " . DB_HOST . "<br>";
echo "DB_NAME: " . DB_NAME . "<br>";
echo "DB_USER: " . DB_USER . "<br>";
echo "DB_PASS: " . (empty(DB_PASS) ? '(空密碼)' : '(已設定密碼)') . "<br>";
echo "DB_CHARSET: " . DB_CHARSET . "<br><br>";

echo "<h3>2. MySQLi 擴展檢查</h3>";
if (extension_loaded('mysqli')) {
    echo "✅ MySQLi 擴展已載入<br><br>";
} else {
    echo "❌ MySQLi 擴展未載入<br><br>";
    exit;
}

echo "<h3>3. 資料庫連接測試</h3>";
try {
    $connection = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($connection->connect_error) {
        throw new Exception("連接失敗: " . $connection->connect_error);
    }
    
    echo "✅ 資料庫連接成功<br>";
    echo "伺服器版本: " . $connection->server_info . "<br>";
    echo "客戶端版本: " . $connection->client_info . "<br><br>";
    
    // 設定字符集
    $connection->set_charset(DB_CHARSET);
    echo "✅ 字符集設定為: " . DB_CHARSET . "<br><br>";
    
    echo "<h3>4. 資料表檢查</h3>";
    $tables = ['receipts', 'receipt_items', 'pc_parts'];
    
    foreach ($tables as $table) {
        $result = $connection->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "✅ 資料表 '$table' 存在<br>";
            
            // 檢查資料表結構
            $structure = $connection->query("DESCRIBE $table");
            if ($structure) {
                echo "&nbsp;&nbsp;&nbsp;欄位數量: " . $structure->num_rows . "<br>";
            }
        } else {
            echo "❌ 資料表 '$table' 不存在<br>";
        }
    }
    
    echo "<br><h3>5. receipts 資料表詳細結構</h3>";
    $structure = $connection->query("DESCRIBE receipts");
    if ($structure) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>欄位名稱</th><th>類型</th><th>允許NULL</th><th>鍵</th><th>預設值</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ 無法獲取 receipts 資料表結構<br>";
    }
    
    echo "<br><h3>6. 測試插入操作</h3>";
    
    // 測試插入一筆收據記錄
    $testSql = "INSERT INTO receipts (
        receipt_number, customer_name, customer_phone, customer_email, 
        customer_address, subtotal, tax_amount, discount_amount, 
        total_amount, payment_method, notes, receipt_date
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $connection->prepare($testSql);
    if ($stmt) {
        $receiptNumber = 'TEST-' . date('YmdHis');
        $customerName = 'Test Customer';
        $customerPhone = '************';
        $customerEmail = '<EMAIL>';
        $customerAddress = 'Test Address';
        $subtotal = 100.00;
        $taxAmount = 0.00;
        $discountAmount = 0.00;
        $totalAmount = 100.00;
        $paymentMethod = 'Cash';
        $notes = 'Test receipt';
        $receiptDate = date('Y-m-d');
        
        $stmt->bind_param('sssssdddssss', 
            $receiptNumber, $customerName, $customerPhone, $customerEmail,
            $customerAddress, $subtotal, $taxAmount, $discountAmount,
            $totalAmount, $paymentMethod, $notes, $receiptDate
        );
        
        if ($stmt->execute()) {
            $insertId = $connection->insert_id;
            echo "✅ 測試插入成功，收據 ID: $insertId<br>";
            
            // 清理測試資料
            $connection->query("DELETE FROM receipts WHERE id = $insertId");
            echo "✅ 測試資料已清理<br>";
        } else {
            echo "❌ 測試插入失敗: " . $stmt->error . "<br>";
        }
        
        $stmt->close();
    } else {
        echo "❌ 準備語句失敗: " . $connection->error . "<br>";
    }
    
    $connection->close();
    
} catch (Exception $e) {
    echo "❌ 錯誤: " . $e->getMessage() . "<br>";
}

echo "<br><h3>7. PHP 環境資訊</h3>";
echo "PHP 版本: " . phpversion() . "<br>";
echo "伺服器軟體: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "文件路徑: " . __FILE__ . "<br>";
?>
